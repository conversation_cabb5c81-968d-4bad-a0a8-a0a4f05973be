#!/bin/bash
set -e

echo "🚀 Flutter container started."

# Pré-cache dos SDKs Android para acelerar builds
echo "📥 Precaching Flutter SDKs..."
flutter precache --android --force

# Configura para desabilitar analytics (só precisa rodar uma vez)
flutter config --no-analytics

# Cria projeto se necessário
if [ ! -f "pubspec.yaml" ]; then
  echo "📦 Creating Flutter project..."
  flutter create . --project-name hello_world_app
fi

echo "Running flutter pub get in app..."
flutter pub get

# Rodar flutter doctor apenas se não existir um arquivo .flutter_doctor_done (marcador)
if [ ! -f "/tmp/.flutter_doctor_done" ]; then
  echo "Running flutter doctor (only once)..."
  # flutter doctor -v
  flutter --version
  touch /tmp/.flutter_doctor_done
else
  echo "Skipping flutter doctor to speed up startup."
fi

echo "Building APK..."
flutter build apk --release

echo "✅ Build finished. APK at: build/app/outputs/flutter-apk/app-release.apk"

exec bash
