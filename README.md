# Flutter Hello World App with Docker

A simple Flutter "Hello World" application that compiles to Android using Docker with a fast live development workflow.

## Features

- 🌍 Simple Hello World Flutter app
- 🐳 Optimized Docker setup with multi-stage builds
- 📱 Android APK compilation
- ⚡ Fast live development workflow
- 🗂️ Persistent caches for dependencies and builds
- 🛠️ Helper scripts for common tasks

## Quick Start

### Option 1: Using the helper script (Recommended)

1. **Start the development environment:**
   ```bash
   ./dev.sh start
   ```

2. **Build your APK:**
   ```bash
   ./dev.sh build    # For release APK
   ./dev.sh debug    # For debug APK
   ```

3. **Enter the container for development:**
   ```bash
   ./dev.sh shell
   ```

### Option 2: Using Docker Compose directly

1. **Build and start the container:**
   ```bash
   docker compose up --build -d
   ```

2. **Build APK manually:**
   ```bash
   docker compose exec flutter-app bash -lc "flutter pub get && flutter build apk --release"
   ```

3. **Copy APK to host:**
   ```bash
   docker compose exec flutter-app bash -lc "cp build/app/outputs/flutter-apk/app-release.apk /app/"
   ```

## Live Development Workflow

This setup is optimized for fast "edit → build" cycles:

1. **Edit your code** in `lib/main.dart` (or any file) using your favorite editor on your host machine
2. **Changes are immediately reflected** in the container via bind mount
3. **Build quickly** using cached dependencies:
   ```bash
   ./dev.sh build    # Usually takes <10 seconds after first build
   ```

### Why it's fast:
- ✅ **Heavy downloads cached in Docker layers** (Android SDK, Flutter engine, etc.)
- ✅ **Pub packages cached in persistent volumes** (no re-download)
- ✅ **Gradle dependencies cached** (incremental builds)
- ✅ **Source code bind-mounted** (instant reflection of changes)

## Helper Scripts

The `dev.sh` script provides convenient commands:

```bash
./dev.sh start     # Start the development container
./dev.sh stop      # Stop the development container
./dev.sh shell     # Enter the container shell
./dev.sh build     # Build release APK
./dev.sh debug     # Build debug APK
./dev.sh clean     # Clean Flutter build cache
./dev.sh pub       # Run flutter pub get
./dev.sh doctor    # Run flutter doctor
./dev.sh logs      # Show container logs
./dev.sh restart   # Restart the container
./dev.sh help      # Show help message
```

## Development Commands

Once inside the container (`./dev.sh shell`), you can run Flutter commands:
```bash
flutter run
flutter build apk --release
flutter build apk --debug
flutter doctor
flutter clean
flutter pub get
```

## Project Structure

```
├── docker-compose.yml      # Docker Compose configuration
├── dockerfile             # Multi-stage Dockerfile for optimized builds
├── dev.sh                 # Development helper script
├── build-apk.sh           # Release APK build script
├── build-debug.sh         # Debug APK build script
├── entrypoint.sh          # Legacy setup script (can be removed)
├── lib/
│   └── main.dart          # Main Flutter app
├── pubspec.yaml           # Flutter dependencies
└── README.md              # This file
```

## Requirements

- Docker
- Docker Compose

The container handles all Flutter and Android SDK dependencies automatically!

## Migration from Previous Setup

If you were using the old setup with automatic APK builds on container start:

1. The new setup **removes automatic builds** for faster container startup
2. Use `./dev.sh build` or `./build-apk.sh` to build APKs on demand
3. **Caches are now persistent** - dependencies won't be re-downloaded
4. **Source code changes are instant** - no need to rebuild the container

## Troubleshooting

### Container won't start
```bash
./dev.sh stop
docker compose down -v  # Remove volumes if needed
./dev.sh start
```

### Build is slow
- First build will be slow (downloading dependencies)
- Subsequent builds should be much faster due to caching
- Use `./dev.sh clean` if you encounter build issues

### APK not found
- Make sure the build completed successfully
- Check `./dev.sh logs` for error messages
- Try `./dev.sh clean` followed by `./dev.sh build`
