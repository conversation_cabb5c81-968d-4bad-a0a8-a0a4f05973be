#!/usr/bin/env bash

# Fast debug APK build script for live development
# This script runs flutter commands inside the Docker container

set -e

echo "🚀 Building debug APK using Docker container..."

# Check if container is running
if ! docker compose ps flutter-app | grep -q "Up"; then
    echo "📦 Starting Flutter development container..."
    docker compose up -d
    echo "⏳ Waiting for container to be ready..."
    sleep 3
fi

# Run pub get and build debug APK
echo "📥 Getting dependencies and building debug APK..."
docker compose exec flutter-app bash -lc "flutter pub get && flutter build apk --debug"

# Copy APK to host
echo "📱 Copying debug APK to host..."
docker compose exec flutter-app bash -lc "cp build/app/outputs/flutter-apk/app-debug.apk /app/app-debug.apk" 2>/dev/null || true

if [ -f "./app-debug.apk" ]; then
    echo "✅ Debug APK built successfully: ./app-debug.apk"
    ls -lh ./app-debug.apk
else
    echo "❌ Debug APK build failed or APK not found"
    exit 1
fi
