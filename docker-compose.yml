services:
  flutter-app:
    build:
      context: .
      # (If your Dockerfile is named "dockerfile" in CWD, no need to specify "dockerfile".)
      target: dev            # ← Use the "dev" stage from our Dockerfile
    image: flutter_dev:latest
    container_name: flutter_dev_container

    # Mount your project folder into /app, so edits are instant
    volumes:
      - .:/app                     # Host's project → container's /app
      # Persist Gradle & pub caches between container runs:
      - flutter_pub_cache:/root/.pub-cache
      - flutter_gradle_cache:/root/.gradle

    environment:
      - FLUTTER_ROOT=/opt/flutter
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
      - JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

    working_dir: /app
    stdin_open: true  # Keep STDIN open so you can "docker-compose exec flutter-app bash"
    tty: true

    # You can map ports if you run "flutter run" (e.g. debugging)
    # For example, if you want to connect to <PERSON><PERSON><PERSON>'s observatory:
    ports:
      - "8095:8080"

    networks:
      - flutter_network

# Declare named volumes for caching:
volumes:
  flutter_pub_cache:
  flutter_gradle_cache:

networks:
  flutter_network:
    driver: bridge
