services:
  flutter-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: flutter_hello_world2
    working_dir: /app
    volumes:
      - .:/app:cached
    environment:
      - FLUTTER_ROOT=/flutter
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
      - JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
    entrypoint: ["/entrypoint.sh"]
    stdin_open: true
    tty: true
    ports:
      - "8090:8080"
    networks:
      - flutter_network

networks:
  flutter_network:
    driver: bridge
