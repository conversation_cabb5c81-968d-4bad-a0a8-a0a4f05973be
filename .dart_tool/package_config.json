{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/async-2.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "characters", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/characters-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/collection-1.17.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cupertino_icons", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fake_async", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///sdks/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_lints", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_test", "rootUri": "file:///sdks/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "js", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/js-0.6.5", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "lints", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/lints-2.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "matcher", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/matcher-0.12.13", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "material_color_utilities", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "meta", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/meta-1.8.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/path-1.8.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///sdks/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_span", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/source_span-1.9.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "stack_trace", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/stack_trace-1.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/stream_channel-2.1.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "string_scanner", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/test_api-0.4.16", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "vector_math", "rootUri": "file:///root/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "hello_world_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "2.19"}], "generated": "2025-06-02T21:02:54.016192Z", "generator": "pub", "generatorVersion": "2.19.4"}