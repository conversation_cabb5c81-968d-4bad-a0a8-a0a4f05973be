#!/usr/bin/env bash

# Fast APK build script for live development
# This script runs flutter commands inside the Docker container

set -e

echo "🚀 Building APK using Docker container..."

# Check if container is running
if ! docker compose ps flutter-app | grep -q "Up"; then
    echo "📦 Starting Flutter development container..."
    docker compose up -d
    echo "⏳ Waiting for container to be ready..."
    sleep 3
fi

# Run pub get and build APK
echo "📥 Getting dependencies and building APK..."
docker compose exec flutter-app bash -lc "flutter pub get && flutter build apk --release"

# Copy APK to host
echo "📱 Copying APK to host..."
docker compose exec flutter-app bash -lc "cp build/app/outputs/flutter-apk/app-release.apk /app/app-release.apk" 2>/dev/null || true

if [ -f "./app-release.apk" ]; then
    echo "✅ APK built successfully: ./app-release.apk"
    ls -lh ./app-release.apk
else
    echo "❌ APK build failed or APK not found"
    exit 1
fi
