FROM cirrusci/flutter:stable

WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
    unzip \
    git \
    curl \
    openjdk-11-jdk \
    build-essential \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*

ENV FLUTTER_ROOT=/flutter
ENV ANDROID_HOME=/opt/android-sdk
ENV ANDROID_SDK_ROOT=/opt/android-sdk
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
ENV PATH=$FLUTTER_ROOT/bin:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH

# ✅ Corrigido: flags sem "=false"
RUN flutter config --no-analytics && \
    flutter config --no-enable-linux-desktop && \
    flutter config --no-enable-macos-desktop && \
    flutter config --no-enable-windows-desktop && \
    flutter config --no-enable-web

# Precache apenas Android
RUN flutter precache --android --force

# Build fake project para cache
RUN flutter create /tmp/dummy && \
    cd /tmp/dummy && \
    flutter pub get && \
    flutter build apk --release && \
    rm -rf /tmp/dummy

VOLUME ["/app"]

COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
