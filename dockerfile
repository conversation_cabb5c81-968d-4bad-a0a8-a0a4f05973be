# ───────────────────────────────
# Stage 0: "builder" with Flutter + Android SDK + caches
# ───────────────────────────────
FROM cirrusci/flutter:stable AS builder

# 1. Install additional packages if needed
RUN apt-get update -qq \
 && apt-get install -y --no-install-recommends \
      build-essential \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*

# 2. Configure Flutter and precache Flutter artifacts
RUN flutter config --no-analytics && \
    flutter config --no-enable-linux-desktop && \
    flutter config --no-enable-macos-desktop && \
    flutter config --no-enable-windows-desktop && \
    flutter config --no-enable-web

RUN flutter precache --android

# 3. Create & prepare /app directory, but do NOT copy your host code just yet:
WORKDIR /app

# We'll use a "partial" pubspec copy step below to cache pub packages.
# First copy only pubspec.* so that pub get is cached (unless pubspec changes):
COPY pubspec.* /app/
RUN flutter pub get

# ────────────────────────────────────────────────────────────────────────────────
# Stage 1: "dev" image (inherits all caches) but with /app left as a mount
# ────────────────────────────────────────────────────────────────────────────────
FROM builder AS dev

# Set environment variables for Java & Android (use existing paths from base image)
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64 \
    FLUTTER_ROOT=/opt/flutter \
    ANDROID_HOME=/opt/android-sdk \
    ANDROID_SDK_ROOT=/opt/android-sdk

# Create a directory for Gradle cache (to persist between runs)
RUN mkdir -p /root/.gradle

# By default, we don't copy the entire host source into the image.
# Instead, we expect /app to be bind-mounted from `docker-compose.yml`.
WORKDIR /app

# Expose an "entrypoint" that just drops you into bash
# You can override this at runtime if you want.
ENTRYPOINT [ "bash" ]
