#!/usr/bin/env bash

# Flutter development helper script
# Provides easy commands for common development tasks

set -e

function show_help() {
    echo "Flutter Development Helper"
    echo ""
    echo "Usage: ./dev.sh [command]"
    echo ""
    echo "Commands:"
    echo "  start     - Start the development container"
    echo "  stop      - Stop the development container"
    echo "  shell     - Enter the container shell"
    echo "  build     - Build release APK"
    echo "  debug     - Build debug APK"
    echo "  clean     - Clean Flutter build cache"
    echo "  pub       - Run flutter pub get"
    echo "  doctor    - Run flutter doctor"
    echo "  logs      - Show container logs"
    echo "  restart   - Restart the container"
    echo "  help      - Show this help message"
    echo ""
}

function ensure_container_running() {
    if ! docker compose ps flutter-app | grep -q "Up"; then
        echo "📦 Starting Flutter development container..."
        docker compose up -d
        echo "⏳ Waiting for container to be ready..."
        sleep 3
    fi
}

case "${1:-help}" in
    "start")
        echo "🚀 Starting Flutter development environment..."
        docker compose up -d
        echo "✅ Container started. Use './dev.sh shell' to enter the container."
        ;;

    "stop")
        echo "🛑 Stopping Flutter development environment..."
        docker compose down
        echo "✅ Container stopped."
        ;;

    "shell")
        ensure_container_running
        echo "🐚 Entering container shell..."
        docker compose exec flutter-app bash
        ;;

    "build")
        ./build-apk.sh
        ;;

    "debug")
        ./build-debug.sh
        ;;

    "clean")
        ensure_container_running
        echo "🧹 Cleaning Flutter build cache..."
        docker compose exec flutter-app bash -lc "flutter clean"
        echo "✅ Build cache cleaned."
        ;;

    "pub")
        ensure_container_running
        echo "📥 Running flutter pub get..."
        docker compose exec flutter-app bash -lc "flutter pub get"
        echo "✅ Dependencies updated."
        ;;

    "doctor")
        ensure_container_running
        echo "🩺 Running flutter doctor..."
        docker compose exec flutter-app bash -lc "flutter doctor -v"
        ;;

    "logs")
        echo "📋 Showing container logs..."
        docker compose logs -f flutter-app
        ;;

    "restart")
        echo "🔄 Restarting Flutter development environment..."
        docker compose restart
        echo "✅ Container restarted."
        ;;
    
    "help"|*)
        show_help
        ;;
esac
